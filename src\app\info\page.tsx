'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { useAccount } from 'wagmi';
import {
  Container,
  Title,
  Card,
  Text,
  Button,
  Group,
  Badge,
  Stack,
  Alert,
  Loader,
  Image,
  Divider,
  TextInput,
  Grid,
  Paper,
  ActionIcon,
  Tooltip,
} from '@mantine/core';
import {
  IconInfoCircle,
  IconWallet,
  IconCoin,
  IconNetwork,
  IconSearch,
  IconExternalLink,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import {
  splitDomain,
  isValidDomainName,
  containsSpecialCharacters,
  getContractConfig,
  checkApi,
  formatAddress,
  NetworkConfig,
  getDomainId,
  getDomainOwner,
  getDomainMintPrice,
  getDomainErcAddress,
  getDomainCommission,
  sleep,
} from '../../lib/library';
import AppShellLayout from '../../components/AppShellLayout';
import { ODUDENAME_CONFIG, consoleLog, consoleError } from '../../lib/config';

interface DomainInfo {
  domain: string;
  chain: string;
  networkConfig: NetworkConfig;
  isAvailable: boolean;
  error?: string;
  domainId?: number;
  owner?: string;
  mintPrice?: string;
  ercAddress?: string;
  commission?: number;
}

export default function InfoPage() {
  const searchParams = useSearchParams();
  const { address, isConnected } = useAccount();
  
  const [domain, setDomain] = useState('');
  const [chain, setChain] = useState('odude');
  const [domainInfo, setDomainInfo] = useState<DomainInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchInput, setSearchInput] = useState('');
  const [showLogin, setShowLogin] = useState(false);
  
    const handleWalletDisconnect = () => {
      consoleLog('HomePage - Wallet disconnected via AppShell');
      setShowLogin(true);
    };

  // Initialize from URL parameters
  useEffect(() => {
    const urlDomain = searchParams.get('domain');
    const urlChain = searchParams.get('chain');
    
    if (urlDomain) {
      setDomain(urlDomain.toLowerCase());
      setSearchInput(urlDomain);
    }
    if (urlChain) {
      setChain(urlChain.toLowerCase());
    }
  }, [searchParams]);

  // Search domain when domain or chain changes
  useEffect(() => {
    if (domain && !containsSpecialCharacters(domain)) {
      searchDomain(domain, chain);
    }
  }, [domain, chain]);

  const searchDomain = async (domainName: string, chainName: string) => {
    if (!domainName) return;

    setLoading(true);
    consoleLog('Searching domain', { domainName, chainName });

    try {
      // Validate domain name
      const validation = isValidDomainName(domainName);
      if (!validation.isValid) {
        setDomainInfo({
          domain: domainName,
          chain: chainName,
          networkConfig: ODUDENAME_CONFIG.NETWORKS.POLYGON,
          isAvailable: false,
          error: validation.error,
        });
        setLoading(false);
        return;
      }

      // Check if domain exists
      const apiUrl = `${ODUDENAME_CONFIG.DOMAIN_ENDPOINT}?domain=${domainName}`;
      const response = await checkApi(apiUrl);
      
      // Get network configuration
      const networkConfig = await getContractConfig(chainName);
      
      const isAvailable = response?.error === 'Error retrieving domain record. execution reverted: No ODude Name found';

      let domainDetails = {};

      // If domain exists, fetch additional details
      if (!isAvailable && !response?.error) {
        try {
          const domainId = await getDomainId(domainName);
          if (domainId) {
            await sleep(500); // Small delay between calls
            const [owner, mintPrice, ercAddress, commission] = await Promise.all([
              getDomainOwner(domainId),
              getDomainMintPrice(domainId),
              getDomainErcAddress(domainId),
              getDomainCommission(domainId),
            ]);

            domainDetails = {
              domainId,
              owner,
              mintPrice,
              ercAddress,
              commission,
            };
          }
        } catch (error) {
          consoleError('Error fetching domain details', error);
        }
      }

      setDomainInfo({
        domain: domainName,
        chain: chainName,
        networkConfig,
        isAvailable,
        error: isAvailable ? undefined : response?.error,
        ...domainDetails,
      });

      consoleLog('Domain search completed', {
        domainName,
        isAvailable,
        networkConfig: networkConfig.name,
      });

    } catch (error) {
      consoleError('Error searching domain', error);
      notifications.show({
        title: 'Search Error',
        message: 'Failed to search domain. Please try again.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    if (searchInput.trim()) {
      setDomain(searchInput.toLowerCase().trim());
      // Update URL
      const url = new URL(window.location.href);
      url.searchParams.set('domain', searchInput.toLowerCase().trim());
      url.searchParams.set('chain', chain);
      window.history.pushState({}, '', url.toString());
    }
  };

  const handleChainChange = (newChain: string) => {
    setChain(newChain);
    // Update URL
    const url = new URL(window.location.href);
    url.searchParams.set('chain', newChain);
    if (domain) {
      url.searchParams.set('domain', domain);
    }
    window.history.pushState({}, '', url.toString());
  };

  const handleMint = () => {
    if (!isConnected) {
      notifications.show({
        title: 'Wallet Required',
        message: 'Please connect your wallet to mint domains.',
        color: 'orange',
      });
      return;
    }

    if (!domainInfo) return;

    consoleLog('Mint button clicked', {
      domain: domainInfo.domain,
      network: domainInfo.networkConfig.name,
      address,
    });

    notifications.show({
      title: 'Minting Started',
      message: `Starting mint process for ${domainInfo.domain}`,
      color: 'blue',
    });

    // TODO: Implement actual minting logic with contract interaction
  };

  const openExplorer = () => {
    if (domainInfo?.networkConfig.explorer) {
      window.open(domainInfo.networkConfig.explorer, '_blank');
    }
  };

  return (
     <AppShellLayout onWalletDisconnect={handleWalletDisconnect}>
    <Container size="lg" py="xl">
      <Stack gap="xl">
        {/* Header */}
        <div>
          <Title order={1} mb="xs">
            Domain Information
          </Title>
          <Text c="dimmed">
            Search and manage your ODude Name domains across different networks
          </Text>
        </div>

        {/* Search Section */}
        <Card shadow="sm" padding="lg" radius="md" withBorder>
          <Stack gap="md">
            <Title order={3}>Search Domain</Title>
            <Grid>
              <Grid.Col span={{ base: 12, md: 8 }}>
                <TextInput
                  placeholder="Enter domain name (e.g., example.crypto)"
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.currentTarget.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  leftSection={<IconSearch size={16} />}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <Button
                  onClick={handleSearch}
                  loading={loading}
                  fullWidth
                  leftSection={<IconSearch size={16} />}
                >
                  Search
                </Button>
              </Grid.Col>
            </Grid>
          </Stack>
        </Card>

        {/* Network Selection */}
        {domainInfo && (
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Group justify="space-between" mb="md">
              <Title order={4}>Select Network</Title>
            </Group>
            <Group>
              <Button
                variant={domainInfo.networkConfig.name === 'POLYGON' ? 'filled' : 'outline'}
                onClick={() => handleChainChange('odude')}
                leftSection={<IconNetwork size={16} />}
              >
                Polygon
              </Button>
              <Button
                variant={domainInfo.networkConfig.name === 'FILECOIN' ? 'filled' : 'outline'}
                onClick={() => handleChainChange('fvm')}
                leftSection={<IconNetwork size={16} />}
              >
                Filecoin
              </Button>
            </Group>
          </Card>
        )}

        {/* Loading State */}
        {loading && (
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Group justify="center">
              <Loader size="md" />
              <Text>Searching domain...</Text>
            </Group>
          </Card>
        )}

        {/* Domain Information */}
        {domainInfo && !loading && (
          <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Stack gap="md">
              <Group justify="space-between">
                <Title order={3}>
                  {domainInfo.isAvailable ? 'TLN Information' : 'TLD Information'}: .{domainInfo.domain}
                </Title>
                <Tooltip label="Open in Explorer">
                  <ActionIcon variant="light" onClick={openExplorer}>
                    <IconExternalLink size={16} />
                  </ActionIcon>
                </Tooltip>
              </Group>

              {domainInfo.error && !domainInfo.isAvailable && (
                <Alert icon={<IconInfoCircle size={16} />} color="red">
                  {domainInfo.error}
                </Alert>
              )}

              {/* Network Banner */}
              <Paper p="md" withBorder>
                <Image
                  src={domainInfo.networkConfig.banner}
                  alt={domainInfo.networkConfig.name}
                  height={100}
                  fit="cover"
                  fallbackSrc="/img/unknown.jpg"
                />
              </Paper>

              {/* Network Details */}
              <Group grow>
                <Badge size="lg" variant="light" color="dark">
                  <Group gap="xs">
                    <IconNetwork size={14} />
                    <span>Blockchain: {domainInfo.networkConfig.name}</span>
                  </Group>
                </Badge>
                <Badge size="lg" variant="light" color="green">
                  <Group gap="xs">
                    <IconCoin size={14} />
                    <span>Fee: {domainInfo.networkConfig.price}</span>
                  </Group>
                </Badge>
                <Badge size="lg" variant="light" color="blue">
                  <Group gap="xs">
                    <IconWallet size={14} />
                    <span>Token: {domainInfo.networkConfig.sign}</span>
                  </Group>
                </Badge>
              </Group>

              {/* Action Button for Available Domains */}
              {domainInfo.isAvailable && (
                <Group justify="center" mt="md">
                  <Button
                    size="lg"
                    onClick={handleMint}
                    disabled={!isConnected}
                    leftSection={<IconWallet size={18} />}
                  >
                    Mint .{domainInfo.domain}
                  </Button>
                </Group>
              )}

              {/* TLD Information for Existing Domains */}
              {!domainInfo.isAvailable && !domainInfo.error && (
                <Stack gap="md" mt="md">
                  <Title order={4}>Domain Details</Title>

                  <Grid>
                    <Grid.Col span={6}>
                      <Paper p="md" withBorder>
                        <Text size="sm" c="dimmed">Domain ID</Text>
                        <Text fw={500}>{domainInfo.domainId || '0'}</Text>
                      </Paper>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Paper p="md" withBorder>
                        <Text size="sm" c="dimmed">Owner</Text>
                        <Text fw={500} style={{ wordBreak: 'break-all' }}>
                          {formatAddress(domainInfo.owner || '0x0000000000000000000000000000000000000000')}
                        </Text>
                      </Paper>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Paper p="md" withBorder>
                        <Text size="sm" c="dimmed">Mint Price</Text>
                        <Text fw={500}>{domainInfo.mintPrice || '0'}</Text>
                      </Paper>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Paper p="md" withBorder>
                        <Text size="sm" c="dimmed">ERC Token Address</Text>
                        <Text fw={500} style={{ wordBreak: 'break-all' }}>
                          {formatAddress(domainInfo.ercAddress || '0x0000000000000000000000000000000000000000')}
                        </Text>
                      </Paper>
                    </Grid.Col>
                    <Grid.Col span={12}>
                      <Paper p="md" withBorder>
                        <Text size="sm" c="dimmed">Earn Commission</Text>
                        <Text fw={500}>{domainInfo.commission || 0}%</Text>
                      </Paper>
                    </Grid.Col>
                  </Grid>

                  {/* Management Section for Domain Owners */}
                  {domainInfo.owner && address && domainInfo.owner.toLowerCase() === address.toLowerCase() && (
                    <Stack gap="md" mt="xl">
                      <Title order={4}>Domain Management</Title>
                      <Alert icon={<IconInfoCircle size={16} />} color="green">
                        You are the owner of this domain. You can manage its settings below.
                      </Alert>

                      <Grid>
                        <Grid.Col span={{ base: 12, md: 6 }}>
                          <Card shadow="sm" padding="lg" radius="md" withBorder>
                            <Stack gap="md">
                              <Title order={5}>Bind Token</Title>
                              <TextInput
                                label="Token Contract Address"
                                placeholder="0x0000000000000000000000000000000000000000"
                                description="Should be of same network chain. If none set address as 0x0000000000000000000000000000000000000000"
                              />
                              <Button variant="outline" fullWidth>
                                Bind Token
                              </Button>
                            </Stack>
                          </Card>
                        </Grid.Col>

                        <Grid.Col span={{ base: 12, md: 6 }}>
                          <Card shadow="sm" padding="lg" radius="md" withBorder>
                            <Stack gap="md">
                              <Title order={5}>Set Price</Title>
                              <TextInput
                                label="Token Price in Unit"
                                placeholder="Enter price"
                                description="Raw value in base units. Use exact decimal places of token."
                              />
                              <Button variant="outline" fullWidth>
                                Set Price
                              </Button>
                            </Stack>
                          </Card>
                        </Grid.Col>
                      </Grid>
                    </Stack>
                  )}

                  {/* Not Owner Message */}
                  {domainInfo.owner && address && domainInfo.owner.toLowerCase() !== address.toLowerCase() && (
                    <Alert icon={<IconInfoCircle size={16} />} color="orange" mt="md">
                      You are not the owner of this domain name. Connected wallet: {formatAddress(address)}
                    </Alert>
                  )}
                </Stack>
              )}

              {/* Connection Status */}
              <Divider />
              <Group justify="center">
                <Text size="sm" c={isConnected ? 'green' : 'red'}>
                  {isConnected ? (
                    <>Connected: {formatAddress(address || '')}</>
                  ) : (
                    'Wallet not connected. Please connect first.'
                  )}
                </Text>
              </Group>
            </Stack>
          </Card>
        )}
      </Stack>
    </Container>
    </AppShellLayout>
  );
}
