'use client';

import { useEffect } from 'react';
import { useAccount, useBalance, useDisconnect } from 'wagmi';
import {
  Group,
  Button,
  Text,
  Menu,
  Avatar,
  Badge,
  ActionIcon,
  Tooltip,
  Box,
  Stack,
} from '@mantine/core';
import {
  IconWallet,
  IconLogout,
  IconCopy,
  IconExternalLink,
  IconChevronDown,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { consoleLog, consoleError } from '../lib/config';
import { formatAddress, formatBalance, getChainName, getExplorerUrl } from '../lib/utils';
import type { HeaderProps } from '../lib/types';

export default function WalletStatus({ onLogout, loading }: HeaderProps) {
  const { address, isConnected, isDisconnected, chain } = useAccount();
  const { data: balance } = useBalance({ address });
  const { disconnect } = useDisconnect();

  // Log wallet status changes
  useEffect(() => {
    consoleLog('WalletStatus - Account state:', {
      address,
      isConnected,
      isDisconnected,
      chainId: chain?.id,
      chainName: chain?.name,
    });
  }, [address, isConnected, isDisconnected, chain]);

  // Log balance changes
  useEffect(() => {
    if (balance) {
      consoleLog('WalletStatus - Balance updated:', {
        formatted: balance.formatted,
        symbol: balance.symbol,
        value: balance.value.toString(),
      });
    }
  }, [balance]);

  const handleDisconnect = async () => {
    try {
      consoleLog('WalletStatus - Disconnecting wallet...');
      disconnect();
      onLogout?.();
      notifications.show({
        title: 'Wallet Disconnected',
        message: 'Your wallet has been disconnected successfully',
        color: 'blue',
      });
    } catch (error) {
      consoleError('WalletStatus - Disconnect error:', error);
      notifications.show({
        title: 'Disconnect Error',
        message: 'Failed to disconnect wallet',
        color: 'red',
      });
    }
  };

  const handleCopyAddress = async () => {
    if (!address) return;
    
    try {
      await navigator.clipboard.writeText(address);
      consoleLog('WalletStatus - Address copied to clipboard:', address);
      notifications.show({
        title: 'Address Copied',
        message: 'Wallet address copied to clipboard',
        color: 'green',
      });
    } catch (error) {
      consoleError('WalletStatus - Copy error:', error);
      notifications.show({
        title: 'Copy Failed',
        message: 'Failed to copy address to clipboard',
        color: 'red',
      });
    }
  };

  const handleViewOnExplorer = () => {
    if (!address || !chain?.id) return;
    
    const explorerUrl = getExplorerUrl(chain.id, address, 'address');
    if (explorerUrl) {
      consoleLog('WalletStatus - Opening explorer:', explorerUrl);
      window.open(explorerUrl, '_blank');
    }
  };

  if (!isConnected || !address) {
    return null;
  }

  return (
    <Group gap="sm">
      {/* Chain Badge */}
      {chain && (
        <Badge
          variant="light"
          color="blue"
          size="sm"
          leftSection={
            chain.iconUrl ? (
              <img
                src={chain.iconUrl}
                alt={chain.name}
                style={{ width: 12, height: 12 }}
              />
            ) : (
              <IconWallet size={12} />
            )
          }
        >
          {getChainName(chain.id)}
        </Badge>
      )}

      {/* Wallet Menu */}
      <Menu shadow="md" width={280} position="bottom-end">
        <Menu.Target>
          <Button
            variant="light"
            leftSection={<Avatar size={20} radius="xl" />}
            rightSection={<IconChevronDown size={14} />}
            loading={loading}
            size="sm"
          >
            {formatAddress(address)}
          </Button>
        </Menu.Target>

        <Menu.Dropdown>
          <Menu.Label>Wallet Information</Menu.Label>
          
          <Box p="sm">
            <Stack gap="xs">
              <Group justify="space-between">
                <Text size="sm" c="dimmed">Address:</Text>
                <Group gap="xs">
                  <Text size="sm" ff="monospace">
                    {formatAddress(address, 8, 6)}
                  </Text>
                  <Tooltip label="Copy address">
                    <ActionIcon
                      size="xs"
                      variant="subtle"
                      onClick={handleCopyAddress}
                    >
                      <IconCopy size={12} />
                    </ActionIcon>
                  </Tooltip>
                </Group>
              </Group>

              {balance && (
                <Group justify="space-between">
                  <Text size="sm" c="dimmed">Balance:</Text>
                  <Text size="sm" fw={500}>
                    {formatBalance(balance.formatted)} {balance.symbol}
                  </Text>
                </Group>
              )}

              {chain && (
                <Group justify="space-between">
                  <Text size="sm" c="dimmed">Network:</Text>
                  <Text size="sm">{chain.name}</Text>
                </Group>
              )}
            </Stack>
          </Box>

          <Menu.Divider />

          <Menu.Item
            leftSection={<IconCopy size={14} />}
            onClick={handleCopyAddress}
          >
            Copy Address
          </Menu.Item>

          {chain && getExplorerUrl(chain.id, address, 'address') && (
            <Menu.Item
              leftSection={<IconExternalLink size={14} />}
              onClick={handleViewOnExplorer}
            >
              View on Explorer
            </Menu.Item>
          )}

          <Menu.Divider />

          <Menu.Item
            color="red"
            leftSection={<IconLogout size={14} />}
            onClick={handleDisconnect}
          >
            Disconnect Wallet
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>
    </Group>
  );
}
