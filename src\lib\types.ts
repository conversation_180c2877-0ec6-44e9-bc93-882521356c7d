// src/lib/types.ts

// Wallet related types
export interface WalletState {
  address: string | null;
  isConnected: boolean;
  isConnecting: boolean;
  isDisconnected: boolean;
  chainId: number | null;
  balance: string | null;
}

export interface WalletConnection {
  address: string;
  chainId: number;
  connector: string;
  timestamp: number;
}

// User types
export interface User {
  id: string;
  walletAddress: string;
  createdAt: Date;
  updatedAt: Date;
  preferences: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  defaultChain: number;
  notifications: boolean;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

// Transaction types
export interface Transaction {
  hash: string;
  from: string;
  to: string;
  value: string;
  gasPrice: string;
  gasLimit: string;
  chainId: number;
  status: 'pending' | 'confirmed' | 'failed';
  timestamp: Date;
}

// Contract types
export interface ContractConfig {
  address: string;
  abi: any[];
  chainId: number;
}

// App state types
export interface AppState {
  wallet: WalletState;
  user: User | null;
  loading: boolean;
  error: string | null;
}

// Component props types
export interface WalletButtonProps {
  variant?: 'filled' | 'outline' | 'light';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
}

export interface HeaderProps {
  walletAddress?: string;
  onLogout?: () => void;
  loading?: boolean;
}

export interface LoginPageProps {
  onConnect?: (address: string) => void;
}

// Event types
export interface WalletEvent {
  type: 'connect' | 'disconnect' | 'chainChanged' | 'accountsChanged';
  data: any;
  timestamp: Date;
}

// Chain types
export interface ChainInfo {
  id: number;
  name: string;
  network: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  rpcUrls: string[];
  blockExplorerUrls: string[];
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
