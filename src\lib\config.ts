// src/lib/config.ts
import { getDefaultConfig } from '@rainbow-me/rainbowkit';
import {
  arbitrum,
  base,
  mainnet,
  optimism,
  polygon,
  sepolia,
} from 'wagmi/chains';

// Wallet configuration
export const config = getDefaultConfig({
  appName: 'ODude Dealer',
  projectId: process.env.NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID || 'YOUR_PROJECT_ID',
  chains: [
    mainnet,
    polygon,
    optimism,
    arbitrum,
    base,
    ...(process.env.NODE_ENV === 'development' ? [sepolia] : []),
  ],
  ssr: true, // If your dApp uses server side rendering (SSR)
});

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000/api',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
} as const;

// App Configuration
export const APP_CONFIG = {
  APP_NAME: 'ODude Dealer',
  APP_DESCRIPTION: 'Top Level Name Management DApp',
  SUPPORTED_CHAINS: [137, 42161, 8453], // mainnet - 1, polygon-137, optimism-10, arbitrum-42161, base-8453
  DEFAULT_CHAIN_ID: 1, // mainnet
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  WALLET_CONNECTED: 'wallet_connected',
  USER_PREFERENCES: 'user_preferences',
  THEME: 'theme',
} as const;

// ODude Name Token Configuration (based on project.txt)
export const ODUDENAME_CONFIG = {
  // Token URLs for different networks
  TOKEN_URL: 'https://polygonscan.com/token/******************************************?a=',
  TOKEN_URL_MUMBAI: 'https://mumbai.polygonscan.com/token/******************************************?a=',
  TOKEN_URL_FIL: 'https://explorer.glif.io/address/******************************************/?',

  // RPC Node URLs
  TOKEN_NODE: 'https://polygon-mainnet.g.alchemy.com/v2/K_A1JxpxF8dGzjk6MT5AhMYqXJj4zryx',
  TOKEN_NODE_MUMBAI: 'https://polygon-mumbai.g.alchemy.com/v2/********************************',
  TOKEN_NODE_FIL: 'https://api.node.glif.io/rpc/v1',

  // Contract Addresses
  TOKEN_CONTRACT: '******************************************',
  TOKEN_CONTRACT_FIL: '******************************************',
  TOKEN_CONTRACT_MUMBAI: '0xf89F5492094b5169C82dfE1cD9C7Ce1C070ca902',

  // Chain Configuration
  TOKEN_CHAIN: 'matic',

  // API Endpoints
  TLN_ENDPOINT: 'https://web3yak.com/endpoint/tln/index.php',
  DOMAIN_ENDPOINT: 'https://web3yak.com/endpoint/v2/index.php',

  // Network Configurations
  NETWORKS: {
    POLYGON: {
      id: '137',
      name: 'POLYGON',
      sign: 'MATIC',
      contract: '******************************************',
      node: 'https://polygon-mainnet.g.alchemy.com/v2/K_A1JxpxF8dGzjk6MT5AhMYqXJj4zryx',
      explorer: 'https://polygonscan.com/token/******************************************?a=',
      price: 300,
      banner: '/img/polygon.jpg'
    },
    FILECOIN: {
      id: '314',
      name: 'FILECOIN',
      sign: 'FIL',
      contract: '******************************************',
      node: 'https://api.node.glif.io/rpc/v1',
      explorer: 'https://explorer.glif.io/address/******************************************/?',
      price: 0.1,
      banner: '/img/filecoin.jpg'
    },
    MUMBAI: {
      id: '80001',
      name: 'MUMBAI',
      sign: 'MUMBAI',
      contract: '0xf89F5492094b5169C82dfE1cD9C7Ce1C070ca902',
      node: 'https://polygon-mumbai.g.alchemy.com/v2/********************************',
      explorer: 'https://mumbai.polygonscan.com/token/******************************************?a=',
      price: 200,
      banner: '/img/unknown.jpg'
    }
  }
} as const;

// Console log configuration
export const CONSOLE_CONFIG = {
  ENABLED: process.env.NODE_ENV === 'development',
  PREFIX: '[ODude]',
} as const;

// Helper function for console logging
export const consoleLog = (message: string, data?: any) => {
  if (CONSOLE_CONFIG.ENABLED) {
    console.log(`${CONSOLE_CONFIG.PREFIX} ${message}`, data || '');
  }
};

export const consoleError = (message: string, error?: any) => {
  if (CONSOLE_CONFIG.ENABLED) {
    console.error(`${CONSOLE_CONFIG.PREFIX} ERROR: ${message}`, error || '');
  }
};

export const consoleWarn = (message: string, data?: any) => {
  if (CONSOLE_CONFIG.ENABLED) {
    console.warn(`${CONSOLE_CONFIG.PREFIX} WARNING: ${message}`, data || '');
  }
};
