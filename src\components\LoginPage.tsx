'use client';

import { useEffect } from 'react';
import { useAccount, useConnect, useDisconnect } from 'wagmi';
import { ConnectButton } from '@rainbow-me/rainbowkit';
import {
  Container,
  Paper,
  Title,
  Text,
  Stack,
  Group,
  Box,
  ThemeIcon,
  Loader,
} from '@mantine/core';
import { IconWallet, IconShieldCheck } from '@tabler/icons-react';
import { consoleLog, consoleError } from '../lib/config';
import { saveWalletConnection, clearWalletConnection } from '../lib/utils';
import type { LoginPageProps } from '../lib/types';

export default function LoginPage({ onConnect }: LoginPageProps) {
  const { address, isConnected, isConnecting, isDisconnected } = useAccount();
  const { connect, connectors, error, isLoading, pendingConnector } = useConnect();
  const { disconnect } = useDisconnect();

  // Log wallet state changes
  useEffect(() => {
    consoleLog('Wallet state changed:', {
      address,
      isConnected,
      isConnecting,
      isDisconnected,
    });

    if (isConnected && address) {
      const connection = {
        address,
        chainId: 1, // Will be updated with actual chain ID
        connector: 'unknown',
        timestamp: Date.now(),
      };
      saveWalletConnection(connection);
      onConnect?.(address);
      consoleLog('Wallet connected successfully', { address });
    }

    if (isDisconnected) {
      clearWalletConnection();
      consoleLog('Wallet disconnected');
    }
  }, [address, isConnected, isConnecting, isDisconnected, onConnect]);

  // Log connection errors
  useEffect(() => {
    if (error) {
      consoleError('Wallet connection error:', error);
    }
  }, [error]);

  // Log connector states
  useEffect(() => {
    consoleLog('Available connectors:', connectors.map(c => ({ id: c.id, name: c.name })));
  }, [connectors]);

  if (isConnecting || isLoading) {
    return (
      <Container size="sm" py={80}>
        <Paper shadow="md" p={30} radius="md" withBorder>
          <Stack align="center" gap="lg">
            <Loader size="lg" />
            <Text size="lg">Connecting to wallet...</Text>
            {pendingConnector && (
              <Text size="sm" c="dimmed">
                Connecting to {pendingConnector.name}
              </Text>
            )}
          </Stack>
        </Paper>
      </Container>
    );
  }

  return (
    <Container size="sm" py={80}>
      <Paper shadow="md" p={30} radius="md" withBorder>
        <Stack align="center" gap="xl">
          {/* Header */}
          <Group gap="md">
            <ThemeIcon size={60} radius="md" variant="gradient" gradient={{ from: 'blue', to: 'cyan' }}>
              <IconWallet size={30} />
            </ThemeIcon>
            <Box>
              <Title order={2} ta="center">
                Welcome to ODude Merchant
              </Title>
              <Text c="dimmed" size="sm" ta="center" mt={5}>
                Connect your wallet to get started
              </Text>
            </Box>
          </Group>

          {/* Features */}
          <Stack gap="md" w="100%">
            <Group gap="sm">
              <ThemeIcon size={24} radius="sm" variant="light" color="green">
                <IconShieldCheck size={16} />
              </ThemeIcon>
              <Text size="sm">Secure wallet connection</Text>
            </Group>
            <Group gap="sm">
              <ThemeIcon size={24} radius="sm" variant="light" color="blue">
                <IconWallet size={16} />
              </ThemeIcon>
              <Text size="sm">Support for multiple wallets</Text>
            </Group>
            <Group gap="sm">
              <ThemeIcon size={24} radius="sm" variant="light" color="violet">
                <IconShieldCheck size={16} />
              </ThemeIcon>
              <Text size="sm">Multi-chain support</Text>
            </Group>
          </Stack>

          {/* Connect Button */}
          <Box w="100%">
            <ConnectButton.Custom>
              {({
                account,
                chain,
                openAccountModal,
                openChainModal,
                openConnectModal,
                authenticationStatus,
                mounted,
              }) => {
                // Note: If your app doesn't use authentication, you
                // can remove all 'authenticationStatus' checks
                const ready = mounted && authenticationStatus !== 'loading';
                const connected =
                  ready &&
                  account &&
                  chain &&
                  (!authenticationStatus ||
                    authenticationStatus === 'authenticated');

                return (
                  <div
                    {...(!ready && {
                      'aria-hidden': true,
                      'style': {
                        opacity: 0,
                        pointerEvents: 'none',
                        userSelect: 'none',
                      },
                    })}
                  >
                    {(() => {
                      if (!connected) {
                        return (
                          <button
                            onClick={openConnectModal}
                            type="button"
                            style={{
                              width: '100%',
                              padding: '12px 24px',
                              backgroundColor: '#3b82f6',
                              color: 'white',
                              border: 'none',
                              borderRadius: '8px',
                              fontSize: '16px',
                              fontWeight: '600',
                              cursor: 'pointer',
                              transition: 'background-color 0.2s',
                            }}
                            onMouseOver={(e) => {
                              e.currentTarget.style.backgroundColor = '#2563eb';
                            }}
                            onMouseOut={(e) => {
                              e.currentTarget.style.backgroundColor = '#3b82f6';
                            }}
                          >
                            Connect Wallet
                          </button>
                        );
                      }

                      if (chain.unsupported) {
                        return (
                          <button
                            onClick={openChainModal}
                            type="button"
                            style={{
                              width: '100%',
                              padding: '12px 24px',
                              backgroundColor: '#ef4444',
                              color: 'white',
                              border: 'none',
                              borderRadius: '8px',
                              fontSize: '16px',
                              fontWeight: '600',
                              cursor: 'pointer',
                            }}
                          >
                            Wrong network
                          </button>
                        );
                      }

                      return (
                        <div style={{ display: 'flex', gap: 12 }}>
                          <button
                            onClick={openChainModal}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              padding: '8px 12px',
                              backgroundColor: '#374151',
                              color: 'white',
                              border: 'none',
                              borderRadius: '8px',
                              fontSize: '14px',
                              cursor: 'pointer',
                            }}
                            type="button"
                          >
                            {chain.hasIcon && (
                              <div
                                style={{
                                  background: chain.iconBackground,
                                  width: 12,
                                  height: 12,
                                  borderRadius: 999,
                                  overflow: 'hidden',
                                  marginRight: 4,
                                }}
                              >
                                {chain.iconUrl && (
                                  <img
                                    alt={chain.name ?? 'Chain icon'}
                                    src={chain.iconUrl}
                                    style={{ width: 12, height: 12 }}
                                  />
                                )}
                              </div>
                            )}
                            {chain.name}
                          </button>

                          <button
                            onClick={openAccountModal}
                            type="button"
                            style={{
                              padding: '8px 12px',
                              backgroundColor: '#374151',
                              color: 'white',
                              border: 'none',
                              borderRadius: '8px',
                              fontSize: '14px',
                              cursor: 'pointer',
                            }}
                          >
                            {account.displayName}
                            {account.displayBalance
                              ? ` (${account.displayBalance})`
                              : ''}
                          </button>
                        </div>
                      );
                    })()}
                  </div>
                );
              }}
            </ConnectButton.Custom>
          </Box>

          {/* Error Display */}
          {error && (
            <Text size="sm" c="red" ta="center">
              {error.message}
            </Text>
          )}
        </Stack>
      </Paper>
    </Container>
  );
}
