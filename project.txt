This is just for reference page


define('ODUDENAME_TOKEN_URL', 'https://polygonscan.com/token/******************************************?a=');
define('ODUDENAME_TOKEN_URL_MUMBAI', 'https://mumbai.polygonscan.com/token/0xf89f5492094b5169c82dfe1cd9c7ce1c070ca902?a=');
define('ODUDENAME_TOKEN_URL_FIL', 'https://explorer.glif.io/address/0x57E34eaDd86A52bA2A13c2f530dBA37bC919F010/?');

define('ODUDENAME_TOKEN_NODE', 'https://polygon-mainnet.g.alchemy.com/v2/********************************');
define('ODUDENAME_TOKEN_NODE_MUMBAI', 'https://polygon-mumbai.g.alchemy.com/v2/********************************');
define('ODUDENAME_TOKEN_NODE_FIL', 'https://api.node.glif.io/rpc/v1');

define('ODUDENAME_TOKEN_CONTRACT', '******************************************');
define('ODUDENAME_TOKEN_CONTRACT_FIL', '******************************************');
define('ODUDENAME_TOKEN_CONTRACT_MUMBAI', '******************************************');

define('ODUDENAME_TOKEN_CHAIN', 'matic');



//Split primary domain by subdomain
	public function split_domain($title, $part)
	{
		//eg.navneet.crypto
		if ($part == "subdomain") {
			$subdomain = explode('.', $title, 2);
			if (isset($subdomain[0])) {
				return $subdomain[0]; //navneet
			}
		} else if ($part == "primary") {
			$subdomain = explode('.', $title, 2);
			if (isset($subdomain[1])) {
				return $subdomain[1]; //crypto
			}
		} else {
			return $title;
		}
	}


    //Validate domain name
	public function is_valid_domain_name($domain_name)
	{
		$dot_count = substr_count($domain_name, '.');
		if ($dot_count > 1)
			return false;

		return (preg_match("/^([a-z\d](-*[a-z\d])*)(\.([a-z\d](-*[a-z\d])*))*$/i", $domain_name) //valid chars check
			&& preg_match("/^.{1,253}$/", $domain_name) //overall length check
			&& preg_match("/^[^\.]{1,63}(\.[^\.]{1,63})*$/", $domain_name)); //length of each label
	}


	function coin_get_contract($title)
	{
		$result = array();

		$primary = strtolower($title);
		$primary = trim($this->split_domain($title, 'primary'));

		if ($primary == '')
			$primary = $title;



		//$index_key_filecoin = array("fil", "fvm", "ipfs", "filecoin");

		$url = "https://web3yak.com/endpoint/tln/index.php?tln=" . urlencode($primary);

		// Get the 'chain' value
		$chain = $this->getChainValue($url);


		if ($chain == '314') {
			$result['contract'] = ODUDENAME_TOKEN_CONTRACT_FIL;
			$result['node'] = ODUDENAME_TOKEN_NODE_FIL;
			$result['explorer'] = ODUDENAME_TOKEN_URL_FIL;
			$result['network'] = '314';
			$result['network_name'] = 'FILECOIN';
			$result['network_sign'] = 'FIL';
			$result['price'] = 0.1;
			$result['banner'] = esc_url(ODUDENAME_PLUGIN_URL . '/public/img/filecoin.jpg');
			return $result;
		} else if ($chain == '80001') {
			$result['contract'] = ODUDENAME_TOKEN_CONTRACT_MUMBAI;
			$result['node'] = ODUDENAME_TOKEN_NODE_MUMBAI;
			$result['explorer'] = ODUDENAME_TOKEN_URL_MUMBAI;
			$result['network'] = '80001';
			$result['network_name'] = 'MUMBAI';
			$result['network_sign'] = 'MUMBAI';
			$result['price'] = 200;
			$result['banner'] = esc_url(ODUDENAME_PLUGIN_URL . '/public/unknown.jpg');
			return $result;
		} else {
			$result['contract'] = ODUDENAME_TOKEN_CONTRACT;
			$result['node'] = ODUDENAME_TOKEN_NODE;
			$result['explorer'] = ODUDENAME_TOKEN_URL;
			$result['network'] = '137';
			$result['network_name'] = 'POLYGON';
			$result['network_sign'] = 'MATIC';
			$result['price'] = 300;
			$result['banner'] = esc_url(ODUDENAME_PLUGIN_URL . '/public/img/polygon.jpg');
			return $result;
		}
	}

    	// Function to execute the URL and get the JSON response
	function getChainValue($url)
	{
		// Fetch the URL content
		$json = file_get_contents($url);
		// Decode the JSON response
		$data = json_decode($json, true);

		// Check if data is an array and has the 'chain' key
		if (is_array($data) && isset($data[0]['chain'])) {
			return $data[0]['chain'];
		} else {
			return null;
		}
	}



<?php
class Odudename_Info
{
    function __construct()
    {
        add_shortcode('odudename-info', array($this, 'domain'));
    }

    public function domain()
    {
        $library = new Odudename_Library();
        // Search
        if (isset($_GET['domain'])) {
            $domain = strtolower(sanitize_text_field($_GET['domain']));
            $domain_test = $library->containsSpecialCharacters($domain);
        } else {
            $domain = '';
        }


        if (isset($_GET['chain'])) {
            $chain = strtolower(sanitize_text_field($_GET['chain']));
        } else {
            $chain = 'yak';
        }

        ob_start();
        if ($domain != "" && !$domain_test) {

            $url = "https://web3domain.org/endpoint/v2/index.php?domain=" . $domain;

            // Get the response from the URL
            $responseData = $library->checkApi($url);
            if ($responseData !== null && isset($responseData['error']) && $responseData['error'] === 'Error retrieving domain record. execution reverted: No ODude Name found') {
                $result = $library->coin_get_contract($chain);
                global $wp;
                $current_url = home_url(add_query_arg(array(), $wp->request));

                $url_polygon = add_query_arg(array(
                    'domain' => $domain,
                    'chain' => 'odude',
                ), $current_url);

                $url_filecoin = add_query_arg(array(
                    'domain' => $domain,
                    'chain' => 'fvm',
                ), $current_url);

                $nft_price = $result['price'];
?>




<div class="fl-buttons fl-has-addons">
    <a href="<?php echo $url_polygon; ?>"
        class="fl-button <?php if ($result['network_name'] == 'POLYGON') echo 'fl-is-success fl-is-selected'; ?>">Polygon</a>
    <a href="<?php echo $url_filecoin; ?>" class=" fl-button
        <?php if ($result['network_name'] == 'FILECOIN') echo 'fl-is-success fl-is-selected'; ?>">Filecoin</a>
</div>


<nav class="fl-panel fl-has-background-white" id="mint_tln_box">
    <p class="fl-panel-heading">TLN Information: .<?php echo $domain; ?>
    </p>
    <div class="fl-panel-block">
        <div class="fl-card">
            <?php


                            ?>
            <img src="<?php echo $result['banner']; ?>">

            <div class="fl-field fl-is-grouped fl-is-grouped-multiline">
                <div class="fl-control">
                    <div class="fl-tags fl-has-addons">
                        <span class="fl-tag fl-is-dark">Blockchain</span>
                        <span class="fl-tag fl-is-warning"><?php echo $result['network_name']; ?></span>
                    </div>
                </div>

                <div class="fl-control">
                    <div class="fl-tags fl-has-addons">
                        <span class="fl-tag fl-is-dark">Fee</span>
                        <span class="fl-tag fl-is-success"><?php echo $result['price']; ?></span>
                    </div>
                </div>

                <div class="fl-control">
                    <div class="fl-tags fl-has-addons">
                        <span class="fl-tag fl-is-dark">Token</span>
                        <span class="fl-tag fl-is-primary"><?php echo $result['network_sign']; ?></span>
                    </div>
                </div>
            </div>

            <p>

                <button class="fl-button fl-is-info fl-is-rounded" id="mint_domain">Mint
                    .<?php echo $domain; ?></button>
            </p>
        </div>
    </div>
</nav>
<div class="fl-notification fl-is-success fl-is-light">

    <div id="json_container">

        <span class="fl-icon-text fl-has-text-success">
            <span class="fl-icon">
                <i class="fas fa-check-circle"></i>
            </span>
        </span>


    </div>
    <div id="crypto_loading">

        <img src="<?php echo esc_url(ODUDENAME_PLUGIN_URL . '/public/img/load.gif'); ?>">
    </div>
</div>
<script>
jQuery(document).ready(function() {

    var claim_name = '<?php echo $domain; ?>';
    var main_chain_network = '<?php echo $result['network']; ?>';
    var contractAddress_mint = '<?php echo $result['contract']; ?>';


    crypto_is_metamask_Connected().then(acc => {
        crypto_is_metamask_Connected().then(acc => {
            if (acc.addr == '') {
                console.log("Metamask not connected. Please connect first");
                jQuery('#json_container').html(
                    '<div class="crypto_alert-box crypto_error">Metamask not connected. Please connect first</div>'
                );
                jQuery("#crypto_loading").hide();
                jQuery('#mint_tln_box').hide();
            } else {

                jQuery('#mint_tln_box').show();
                jQuery("#crypto_loading").hide();

                if ((acc.network != '<?php echo $result['network']; ?>')) {

                    var msg =

                        "Change your network to " + crypto_network_arr[main_chain_network] +

                        ". Your are connected on " +

                        crypto_network_arr[acc.network];

                    jQuery('#json_container').html(

                        '<div class="crypto_alert-box crypto_error">' + msg + '</div>'

                    );
                    jQuery("#crypto_loading").hide();
                } else {
                    console.log("I will connect");

                    jQuery("#mint_domain").click(function() {
                        // alert("mint" + claim_name);
                        jQuery("#crypto_loading").show();

                        web3 = new Web3(window.ethereum);
                        const connectWallet = async () => {
                            const accounts = await ethereum.request({
                                method: "eth_requestAccounts"
                            });
                            var persons = [];
                            account = accounts[0];
                            console.log(`Connected account : ${account}`);
                            var claim_id = crypto_uniqueId();
                            var claim_url =
                                '<?php echo 'https://odude.com/temp/' . strtolower($domain) . '.json'; ?>';
                            var claim_transfer_to = account;
                            var amount = "<?php echo $nft_price; ?>";
                            var domain_claim = await claim(claim_id, claim_name,
                                claim_url, claim_transfer_to, amount);

                            jQuery('#json_container').html('Claim Started...');

                            if (domain_claim == true) {

                                jQuery('#json_container').html(
                                    '<div class="crypto_alert-box crypto_success">Successfully minted and domain transferred to <strong>' +
                                    claim_transfer_to +
                                    '</strong></div>');
                                jQuery("#crypto_claim_box").hide();
                                jQuery("#crypto_loading").hide();

                            } else {
                                jQuery('#json_container').html(
                                    '<div class="crypto_alert-box crypto_notice">' +
                                    domain_claim +
                                    '</div>');
                                jQuery("#crypto_loading").hide();

                            }

                        }

                        console.log("Contract: " + contractAddress_mint);
                        connectWallet();
                        connectContract(contractAbi, contractAddress_mint);


                    });
                }
            }
        });
    });



});
</script>
<?php
            } else {

                $result = $library->coin_get_contract($domain);
            ?>


<nav class="fl-panel fl-has-background-white">
    <p class="fl-panel-heading">TLD Information: <?php echo $domain; ?>
    </p>
    <div class="fl-panel-block">
        <div class="fl-card">
            <div class="fl-tags has-addons">
                <span class="fl-tag"><?php echo $domain; ?> ID </span>
                <span class="fl-tag fl-is-primary" id="domain_id">0</span>
            </div>

            <div class="fl-tags has-addons">
                <span class="fl-tag"><?php echo $domain; ?> Owner </span>
                <span class="fl-tag fl-is-primary" id="domain_owner">******************************************</span>
            </div>

            <div class="fl-tags has-addons">
                <span class="fl-tag"><?php echo $domain; ?> Mint Price </span>
                <span class="fl-tag fl-is-primary" id="domain_mint_price">0</span>
            </div>

            <div class="fl-tags has-addons">
                <span class="fl-tag"><?php echo $domain; ?> ERC Token Address </span>
                <span class="fl-tag fl-is-primary"
                    id="domain_erc_address">******************************************</span>
            </div>

            <div class="fl-tags has-addons">
                <span class="fl-tag"><?php echo $domain; ?> Earn Commission </span>
                <span class="fl-tag fl-is-primary" id="domain_commission">0%</span>
            </div>
        </div>
    </div>
</nav>

<div class="fl-notification fl-is-success fl-is-light">

    <div id="json_container">Connecting ....</div>
    <div id="crypto_loading">

        <img src="<?php echo esc_url(ODUDENAME_PLUGIN_URL . '/public/img/load.gif'); ?>">
    </div>
</div>

<div id="allow_box">

    <div class="fl-columns">
        <div class="fl-column is-half">

            <nav class="fl-panel fl-has-background-white">
                <p class="fl-panel-heading">Bind TOKEN : <?php echo $domain; ?>
                </p>
                <div class="fl-panel-block">
                    <div class="fl-card">
                        <span>
                            <div class="fl-field">
                                <label class="fl-label">Token Contract Address</label>
                                <div class="fl-control">
                                    <input class="fl-input" type="text" placeholder="Text input" id="erc_token_address">
                                </div>
                                <p class="fl-help">* Should be of same network chain.<br>* If none set address as
                                    ******************************************<br> ERC20 Token contract address</p>
                            </div>
                            <div class="fl-field">
                                <div class="fl-control">
                                    <button class="fl-button fl-is-primary" id="set_erc">Bind Token</button>
                                </div>
                            </div>
                        </span>
                    </div>
                </div>
                <br />

            </nav>

        </div>
        <div class="fl-column">


            <nav class="fl-panel fl-has-background-white">
                <p class="fl-panel-heading">Set price : <?php echo $domain; ?>
                </p>
                <div class="fl-panel-block">
                    <div class="fl-card">
                        <span>
                            <div class="fl-field">
                                <label class="fl-label">Token Price in Unit</label>
                                <div class="fl-control">
                                    <input class="fl-input" type="text" placeholder="Text input" id="domain_price">
                                </div>
                                <p class="fl-help">* Raw value in base units<br> * Use exact decimal places of
                                    token.<br> * 1 USDC = 1000000 units<br> * 0.001 ETH = 1000000000000000</p>
                            </div>
                            <div class="fl-field">
                                <div class="fl-control">
                                    <button class="fl-button fl-is-primary" id="set_allow">Set Price</button>
                                </div>
                            </div>
                        </span>
                    </div>
                </div>
                <br />

            </nav>


        </div>



    </div>





</div>
<script>
jQuery('#allow_box').hide();
var claim_name = '<?php echo $domain; ?>';
var domain_id = 0;
console.log(claim_name);

function crypto_start(action) {

    var main_chain_network = '<?php echo $result['network']; ?>';
    var contractAddress_mint = '<?php echo $result['contract']; ?>';

    crypto_is_metamask_Connected().then(acc => {
        crypto_is_metamask_Connected().then(acc => {
            if (acc.addr == '') {
                //console.log("Metamask not connected. Please connect first");
                jQuery('#json_container').html(
                    '<div class="crypto_alert-box crypto_error">Metamask not connected. Please connect first</div>'
                );
                jQuery("#crypto_loading").hide();
            } else {
                jQuery("#crypto_loading").show();
                console.log("Connected to:" + acc.addr + "\n Network:" + acc.network);

                if ((acc.network != '<?php echo $result['network']; ?>')) {

                    var msg =

                        "Change your network to " + crypto_network_arr[main_chain_network] +

                        ". Your are connected on " +

                        crypto_network_arr[acc.network];

                    jQuery('#json_container').html(

                        '<div class="crypto_alert-box crypto_error">' + msg + '</div>'

                    );
                    jQuery("#crypto_loading").hide();
                } else {
                    console.log("I will connect");


                    web3 = new Web3(window.ethereum);
                    const connectWallet = async () => {
                        const accounts = await ethereum.request({
                            method: "eth_requestAccounts"
                        });
                        var persons = [];
                        account = accounts[0];
                        console.log(`Connected account : ${account}`);

                        await crypto_sleep(1000);
                        var get_domain_id = await getId(claim_name);
                        jQuery("#crypto_loading").show();
                        jQuery('#json_container').html('Checking records of ' + claim_name +
                            ' domain....');

                        if (typeof get_domain_id !== 'undefined') {
                            console.log(get_domain_id);
                            jQuery('#domain_id').html(get_domain_id);
                            domain_id = get_domain_id;

                            await crypto_sleep(1000);
                            var get_allow_price = await getAllow(domain_id);
                            jQuery('#domain_mint_price').html(get_allow_price);
                            //	jQuery('#domain_price').val(get_allow_price);

                            var domain_owner = await getOwner(domain_id);
                            await crypto_sleep(1000);

                            var old_erc_address = await get_erc_TLD(domain_id);
                            await crypto_sleep(1000);
                            if (old_erc_address == "") {
                                old_erc_address = '******************************************';
                            }
                            jQuery('#domain_erc_address').html(old_erc_address);
                            //jQuery('#erc_token_address').val(old_erc_address);

                            var get_commission = await get_Comm_TLD(domain_id);
                            await crypto_sleep(1000);
                            jQuery('#domain_commission').html(get_commission + "%");

                            jQuery('#json_container').html('');

                            if (typeof domain_owner !== 'undefined') {

                                jQuery('#domain_owner').html(domain_owner);
                                console.log(domain_id + ': Domain owner ' + domain_owner);

                                if (domain_owner.toLowerCase() === account.toLowerCase()) {

                                    console.log("Authorized");
                                    jQuery('#allow_box').show();
                                    if (action == 'set_erc') {

                                        jQuery('#json_container').html('Bind token started...');

                                        var erc_token_address = jQuery('#erc_token_address')
                                            .val();
                                        if (!erc_token_address) {
                                            alert("ERC20 invalid address");
                                            jQuery('#json_container').html(
                                                '<div class="crypto_alert-box crypto_warning">Couldn\'t bind ERC token with domain.</div>'
                                            );
                                            jQuery("#crypto_loading").hide();
                                        } else {
                                            console.log(domain_id + "---" + erc_token_address);

                                            var set_erc = await set_erc_TLD(domain_id,
                                                erc_token_address);
                                            if (set_erc == true) {
                                                //	crypto_allow_process(post_id, claim_id, set_price);
                                                //jQuery('#subdomain_price').html(set_price);
                                                jQuery('#json_container').html(
                                                    '<div class="crypto_alert-box crypto_success">Successfully domain bind with ERC Token</div>'
                                                );
                                                jQuery("#crypto_loading").hide();

                                            } else {
                                                jQuery('#json_container').html(
                                                    '<div class="crypto_alert-box crypto_warning">Error with ERC Token Address</div>'
                                                );
                                                jQuery("#crypto_loading").hide();
                                            }
                                        }

                                    } else if (action == 'set_price') {

                                        jQuery('#json_container').html('Set price started...');
                                        var set_price = jQuery('#domain_price').val();
                                        var set_price_result = await setAllow(domain_id,
                                            set_price);

                                        if (set_price_result == true) {
                                            //crypto_allow_process(post_id, claim_id, set_price);
                                            jQuery('#domain_mint_price').html(set_price);
                                            jQuery("#crypto_loading").hide();
                                        } else {
                                            jQuery('#json_container').html(
                                                '<div class="crypto_alert-box crypto_warning">Error setting price</div>'
                                            );
                                            jQuery("#crypto_loading").hide();
                                        }

                                    } else {
                                        console.log("Nothing to do");
                                        jQuery("#crypto_loading").hide();
                                    }

                                } else {

                                    jQuery('#json_container').html(
                                        '<div class="crypto_alert-box crypto_warning"> Your are not owner of this domain name.<br>Check your connected wallet address connected as <br>' +
                                        domain_owner + '</div>');
                                    jQuery("#crypto_loading").hide();
                                }

                            } else {

                                jQuery('#json_container').html(
                                    '<div class="crypto_alert-box crypto_notice">Error getting owner address</div>'
                                );
                                jQuery("#crypto_loading").hide();
                            }

                            jQuery('#bind_erc_box').show();
                        } else {
                            jQuery('#json_container').html(
                                '<div class="crypto_alert-box crypto_notice">Error connecting this TLD</div>'
                            );
                            jQuery("#crypto_loading").hide();
                        }

                    }

                    console.log("Contract: " + contractAddress_mint);
                    connectWallet();
                    //console.log(contractAbi);
                    //console.log("-----");
                    connectContract(contractAbi, contractAddress_mint);
                }
            }
        });

    });
}

jQuery(document).ready(function() {

    crypto_start('');

    jQuery("#set_erc").click(function() {
        //alert("bind price");

        crypto_start('set_erc');
    });


    jQuery("#set_allow").click(function() {
        // alert("Set price");

        crypto_start('set_price');
    });


});
</script>

<?php
            }
        }
        $content = ob_get_clean();
        return $content;
    }
}
new Odudename_Info();