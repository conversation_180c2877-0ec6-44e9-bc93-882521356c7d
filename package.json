{"name": "odude-dealer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mantine/core": "^8.1.2", "@mantine/hooks": "^8.1.2", "@mantine/next": "^6.0.22", "@mantine/notifications": "^8.1.2", "@rainbow-me/rainbowkit": "^2.2.8", "@tabler/icons-react": "^3.34.0", "@tanstack/react-query": "^5.81.5", "ethers": "^6.15.0", "next": "15.3.5", "odude-sdk": "file:/E:/odude/npmjs/odude-sdk", "react": "^19.0.0", "react-dom": "^19.0.0", "viem": "^2.31.7", "wagmi": "^2.15.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "typescript": "^5"}}