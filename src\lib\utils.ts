// src/lib/utils.ts
import { consoleLog, consoleError, STORAGE_KEYS } from './config';
import type { WalletConnection, WalletState } from './types';

// Wallet address formatting
export const formatAddress = (address: string, startLength = 6, endLength = 4): string => {
  if (!address) return '';
  if (address.length <= startLength + endLength) return address;
  return `${address.slice(0, startLength)}...${address.slice(-endLength)}`;
};

// Balance formatting
export const formatBalance = (balance: string | number, decimals = 4): string => {
  const num = typeof balance === 'string' ? parseFloat(balance) : balance;
  if (isNaN(num)) return '0';
  return num.toFixed(decimals);
};

// Chain name mapping
export const getChainName = (chainId: number): string => {
  const chainNames: Record<number, string> = {
    1: 'Ethereum',
    137: 'Polygon',
    10: 'Optimism',
    42161: 'Arbitrum',
    8453: 'Base',
    11155111: 'Sepolia',
  };
  return chainNames[chainId] || `Chain ${chainId}`;
};

// Local storage utilities
export const saveWalletConnection = (connection: WalletConnection): void => {
  try {
    localStorage.setItem(STORAGE_KEYS.WALLET_CONNECTED, JSON.stringify(connection));
    consoleLog('Wallet connection saved to localStorage', connection);
  } catch (error) {
    consoleError('Failed to save wallet connection', error);
  }
};

export const getWalletConnection = (): WalletConnection | null => {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.WALLET_CONNECTED);
    if (!stored) return null;
    const connection = JSON.parse(stored) as WalletConnection;
    consoleLog('Wallet connection retrieved from localStorage', connection);
    return connection;
  } catch (error) {
    consoleError('Failed to retrieve wallet connection', error);
    return null;
  }
};

export const clearWalletConnection = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEYS.WALLET_CONNECTED);
    consoleLog('Wallet connection cleared from localStorage');
  } catch (error) {
    consoleError('Failed to clear wallet connection', error);
  }
};

// Wallet state utilities
export const createInitialWalletState = (): WalletState => ({
  address: null,
  isConnected: false,
  isConnecting: false,
  isDisconnected: true,
  chainId: null,
  balance: null,
});

// Validation utilities
export const isValidAddress = (address: string): boolean => {
  return /^0x[a-fA-F0-9]{40}$/.test(address);
};

export const isValidChainId = (chainId: number): boolean => {
  return chainId > 0 && Number.isInteger(chainId);
};

// Error handling utilities
export const handleWalletError = (error: any): string => {
  consoleError('Wallet error occurred', error);
  
  if (error?.code === 4001) {
    return 'User rejected the connection request';
  }
  
  if (error?.code === -32002) {
    return 'Connection request already pending';
  }
  
  if (error?.message?.includes('No provider')) {
    return 'No wallet provider found. Please install a wallet extension.';
  }
  
  return error?.message || 'An unknown wallet error occurred';
};

// Async utilities
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// URL utilities
export const getExplorerUrl = (chainId: number, hash: string, type: 'tx' | 'address' = 'tx'): string => {
  const explorers: Record<number, string> = {
    1: 'https://etherscan.io',
    137: 'https://polygonscan.com',
    10: 'https://optimistic.etherscan.io',
    42161: 'https://arbiscan.io',
    8453: 'https://basescan.org',
    11155111: 'https://sepolia.etherscan.io',
  };
  
  const baseUrl = explorers[chainId];
  if (!baseUrl) return '';
  
  return `${baseUrl}/${type}/${hash}`;
};
