// src/app/layout.tsx
import '@rainbow-me/rainbowkit/styles.css';
import '@mantine/core/styles.css';
import '@mantine/notifications/styles.css';

import { Providers } from './providers';

export const metadata = {
  title: 'ODude Dealer',
  description: 'Your Top Level Name Management DApp',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
