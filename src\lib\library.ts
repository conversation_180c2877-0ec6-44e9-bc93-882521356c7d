// src/lib/library.ts
// Common functions for ODude Name management based on project.txt

import { ODUDENAME_CONFIG, consoleLog, consoleError } from './config';

// Network configuration type
export interface NetworkConfig {
  id: string;
  name: string;
  sign: string;
  contract: string;
  node: string;
  explorer: string;
  price: number;
  banner: string;
}

// Domain validation result
export interface DomainValidationResult {
  isValid: boolean;
  error?: string;
}

// API response types
export interface ChainResponse {
  chain: string;
}

export interface DomainResponse {
  error?: string;
  [key: string]: any;
}

/**
 * Split domain by subdomain or primary part
 * Based on PHP split_domain function
 */
export function splitDomain(title: string, part: 'subdomain' | 'primary'): string {
  consoleLog('splitDomain called', { title, part });
  
  if (part === 'subdomain') {
    const subdomain = title.split('.', 2);
    if (subdomain[0]) {
      return subdomain[0]; // e.g., "navneet" from "navneet.crypto"
    }
  } else if (part === 'primary') {
    const subdomain = title.split('.', 2);
    if (subdomain[1]) {
      return subdomain[1]; // e.g., "crypto" from "navneet.crypto"
    }
  }
  
  return title;
}

/**
 * Validate domain name
 * Based on PHP is_valid_domain_name function
 */
export function isValidDomainName(domainName: string): DomainValidationResult {
  consoleLog('isValidDomainName called', { domainName });
  
  const dotCount = (domainName.match(/\./g) || []).length;
  if (dotCount > 1) {
    return { isValid: false, error: 'Too many dots in domain name' };
  }

  // Valid chars check
  const validCharsRegex = /^([a-z\d](-*[a-z\d])*)(\.([a-z\d](-*[a-z\d])*))*$/i;
  if (!validCharsRegex.test(domainName)) {
    return { isValid: false, error: 'Invalid characters in domain name' };
  }

  // Overall length check
  const overallLengthRegex = /^.{1,253}$/;
  if (!overallLengthRegex.test(domainName)) {
    return { isValid: false, error: 'Domain name too long' };
  }

  // Length of each label
  const labelLengthRegex = /^[^\.]{1,63}(\.[^\.]{1,63})*$/;
  if (!labelLengthRegex.test(domainName)) {
    return { isValid: false, error: 'Domain label too long' };
  }

  return { isValid: true };
}

/**
 * Check if string contains special characters
 * Helper function for domain validation
 */
export function containsSpecialCharacters(str: string): boolean {
  const specialCharsRegex = /[^a-zA-Z0-9.-]/;
  return specialCharsRegex.test(str);
}

/**
 * Get chain value from TLN endpoint
 * Based on PHP getChainValue function
 */
export async function getChainValue(tln: string): Promise<string | null> {
  consoleLog('getChainValue called', { tln });
  
  try {
    const url = `${ODUDENAME_CONFIG.TLN_ENDPOINT}?tln=${encodeURIComponent(tln)}`;
    const response = await fetch(url);
    
    if (!response.ok) {
      consoleError('Failed to fetch chain value', response.statusText);
      return null;
    }
    
    const data: ChainResponse[] = await response.json();
    
    if (Array.isArray(data) && data.length > 0 && data[0].chain) {
      consoleLog('Chain value retrieved', data[0].chain);
      return data[0].chain;
    }
    
    return null;
  } catch (error) {
    consoleError('Error fetching chain value', error);
    return null;
  }
}

/**
 * Get contract configuration based on domain/chain
 * Based on PHP coin_get_contract function
 */
export async function getContractConfig(title: string): Promise<NetworkConfig> {
  consoleLog('getContractConfig called', { title });
  
  const primary = splitDomain(title.toLowerCase().trim(), 'primary') || title;
  const chain = await getChainValue(primary);
  
  consoleLog('Chain determined', { primary, chain });
  
  if (chain === '314') {
    return ODUDENAME_CONFIG.NETWORKS.FILECOIN;
  } else if (chain === '80001') {
    return ODUDENAME_CONFIG.NETWORKS.MUMBAI;
  } else {
    return ODUDENAME_CONFIG.NETWORKS.POLYGON;
  }
}

/**
 * Check API for domain information
 * Based on PHP API checking functionality
 */
export async function checkApi(url: string): Promise<DomainResponse | null> {
  consoleLog('checkApi called', { url });
  
  try {
    const response = await fetch(url);
    
    if (!response.ok) {
      consoleError('API request failed', response.statusText);
      return null;
    }
    
    const data: DomainResponse = await response.json();
    consoleLog('API response received', data);
    
    return data;
  } catch (error) {
    consoleError('Error checking API', error);
    return null;
  }
}

/**
 * Generate unique ID for claims
 * Helper function for minting process
 */
export function generateUniqueId(): string {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
}

/**
 * Format address for display
 * Helper function to truncate wallet addresses
 */
export function formatAddress(address: string, startLength = 6, endLength = 4): string {
  if (!address) return '';
  if (address.length <= startLength + endLength) return address;
  
  return `${address.slice(0, startLength)}...${address.slice(-endLength)}`;
}

/**
 * Sleep function for async operations
 * Helper function for contract interactions
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Validate Ethereum address
 * Helper function for address validation
 */
export function isValidEthereumAddress(address: string): boolean {
  const ethereumAddressRegex = /^0x[a-fA-F0-9]{40}$/;
  return ethereumAddressRegex.test(address);
}

/**
 * Get network name by chain ID
 * Helper function for network identification
 */
export function getNetworkNameByChainId(chainId: string): string {
  const networks = ODUDENAME_CONFIG.NETWORKS;
  
  for (const [key, network] of Object.entries(networks)) {
    if (network.id === chainId) {
      return network.name;
    }
  }
  
  return 'Unknown Network';
}

/**
 * Build domain URL with parameters
 * Helper function for URL construction
 */
export function buildDomainUrl(baseUrl: string, domain: string, chain: string): string {
  const url = new URL(baseUrl);
  url.searchParams.set('domain', domain);
  url.searchParams.set('chain', chain);
  return url.toString();
}

// Contract interaction types and interfaces
export interface ContractCallResult {
  success: boolean;
  data?: any;
  error?: string;
}

/**
 * Mock contract interaction functions
 * These would be replaced with actual Web3 contract calls
 */

/**
 * Get domain ID by name
 * Mock implementation of contract getID function
 */
export async function getDomainId(domainName: string): Promise<number | null> {
  consoleLog('getDomainId called', { domainName });

  // Mock implementation - in real app this would call the contract
  // return await contract.getID(domainName);

  // For demo purposes, return a mock ID
  return Math.floor(Math.random() * 1000) + 1;
}

/**
 * Get domain owner by token ID
 * Mock implementation of contract getOwner function
 */
export async function getDomainOwner(tokenId: number): Promise<string | null> {
  consoleLog('getDomainOwner called', { tokenId });

  // Mock implementation - in real app this would call the contract
  // return await contract.getOwner(tokenId);

  // For demo purposes, return a mock address
  return '******************************************';
}

/**
 * Get domain mint price by token ID
 * Mock implementation of contract getAllow function
 */
export async function getDomainMintPrice(tokenId: number): Promise<string | null> {
  consoleLog('getDomainMintPrice called', { tokenId });

  // Mock implementation - in real app this would call the contract
  // return await contract.getAllow(tokenId);

  // For demo purposes, return a mock price
  return '1000000000000000000'; // 1 ETH in wei
}

/**
 * Get ERC token address for domain
 * Mock implementation of contract get_erc_TLD function
 */
export async function getDomainErcAddress(tokenId: number): Promise<string | null> {
  consoleLog('getDomainErcAddress called', { tokenId });

  // Mock implementation - in real app this would call the contract
  // return await contract.get_erc_TLD(tokenId);

  // For demo purposes, return zero address
  return '******************************************';
}

/**
 * Get commission percentage for domain
 * Mock implementation of contract get_Comm_TLD function
 */
export async function getDomainCommission(tokenId: number): Promise<number | null> {
  consoleLog('getDomainCommission called', { tokenId });

  // Mock implementation - in real app this would call the contract
  // return await contract.get_Comm_TLD(tokenId);

  // For demo purposes, return a mock commission
  return 5; // 5%
}

/**
 * Set ERC token address for domain
 * Mock implementation of contract set_erc_TLD function
 */
export async function setDomainErcAddress(tokenId: number, ercAddress: string): Promise<ContractCallResult> {
  consoleLog('setDomainErcAddress called', { tokenId, ercAddress });

  // Mock implementation - in real app this would call the contract
  // const tx = await contract.set_erc_TLD(tokenId, ercAddress);
  // await tx.wait();

  // For demo purposes, simulate success
  await sleep(2000); // Simulate transaction time
  return { success: true };
}

/**
 * Set domain mint price
 * Mock implementation of contract setAllow function
 */
export async function setDomainMintPrice(tokenId: number, price: string): Promise<ContractCallResult> {
  consoleLog('setDomainMintPrice called', { tokenId, price });

  // Mock implementation - in real app this would call the contract
  // const tx = await contract.setAllow(tokenId, price);
  // await tx.wait();

  // For demo purposes, simulate success
  await sleep(2000); // Simulate transaction time
  return { success: true };
}

/**
 * Claim/mint a domain
 * Mock implementation of contract claim function
 */
export async function claimDomain(
  tokenId: number,
  domainName: string,
  tokenUri: string,
  toAddress: string
): Promise<ContractCallResult> {
  consoleLog('claimDomain called', { tokenId, domainName, tokenUri, toAddress });

  // Mock implementation - in real app this would call the contract
  // const tx = await contract.claim(tokenId, domainName, tokenUri, toAddress, { value: price });
  // await tx.wait();

  // For demo purposes, simulate success
  await sleep(3000); // Simulate transaction time
  return { success: true };
}

/**
 * Check if wallet is connected to the correct network
 * Helper function for network validation
 */
export function isCorrectNetwork(currentChainId: number, requiredChainId: string): boolean {
  return currentChainId.toString() === requiredChainId;
}

/**
 * Get network configuration by chain ID
 * Helper function to get network config
 */
export function getNetworkConfigByChainId(chainId: string): NetworkConfig | null {
  const networks = ODUDENAME_CONFIG.NETWORKS;

  for (const network of Object.values(networks)) {
    if (network.id === chainId) {
      return network;
    }
  }

  return null;
}

/**
 * Format price for display
 * Helper function to format token amounts
 */
export function formatPrice(price: string, decimals: number = 18): string {
  try {
    const priceNum = parseFloat(price);
    const divisor = Math.pow(10, decimals);
    const formatted = (priceNum / divisor).toFixed(4);
    return parseFloat(formatted).toString(); // Remove trailing zeros
  } catch {
    return price;
  }
}

/**
 * Validate token address format
 * Helper function for ERC20 address validation
 */
export function isValidTokenAddress(address: string): boolean {
  if (!address) return false;

  // Allow zero address
  if (address === '******************************************') {
    return true;
  }

  return isValidEthereumAddress(address);
}

/**
 * Generate token URI for domain
 * Helper function to create metadata URI
 */
export function generateTokenUri(domain: string): string {
  // In real implementation, this would point to actual metadata
  return `https://odude.com/temp/${domain.toLowerCase()}.json`;
}

/**
 * Parse error message from contract call
 * Helper function to extract readable error messages
 */
export function parseContractError(error: any): string {
  if (typeof error === 'string') {
    return error;
  }

  if (error?.message) {
    return error.message;
  }

  if (error?.reason) {
    return error.reason;
  }

  return 'Unknown error occurred';
}
