'use client';

import { useEffect, useState } from 'react';
import { useAccount, useBalance } from 'wagmi';
import {
  Text,
  Container,
  Stack,
  Card,
  Group,
  Badge,
  Title,
  Grid,
  Paper,
  ThemeIcon,
  Box,
  Loader,
  Center,
} from '@mantine/core';
import {
  IconWallet,
  IconNetwork,
  IconCoins,
  IconActivity,
} from '@tabler/icons-react';
import AppShellLayout from '../components/AppShellLayout';
import LoginPage from '../components/LoginPage';
import { consoleLog, consoleError } from '../lib/config';
import { formatBalance, getChainName } from '../lib/utils';

export default function HomePage() {
  const { address, isConnected, isConnecting, isDisconnected, chain } = useAccount();
  const { data: balance, isLoading: balanceLoading } = useBalance({ address });
  const [showLogin, setShowLogin] = useState(false);

  // Log all wallet state changes
  useEffect(() => {
    consoleLog('HomePage - Wallet state changed:', {
      address,
      isConnected,
      isConnecting,
      isDisconnected,
      chainId: chain?.id,
      chainName: chain?.name,
    });
  }, [address, isConnected, isConnecting, isDisconnected, chain]);

  // Log balance changes
  useEffect(() => {
    if (balance) {
      consoleLog('HomePage - Balance loaded:', {
        formatted: balance.formatted,
        symbol: balance.symbol,
        value: balance.value.toString(),
      });
    }
  }, [balance]);

  // Handle wallet connection state
  useEffect(() => {
    if (isDisconnected) {
      consoleLog('HomePage - Wallet disconnected, showing login page');
      setShowLogin(true);
    } else if (isConnected) {
      consoleLog('HomePage - Wallet connected, showing main content');
      setShowLogin(false);
    }
  }, [isConnected, isDisconnected]);

  const handleWalletConnect = (walletAddress: string) => {
    consoleLog('HomePage - Wallet connected via LoginPage:', walletAddress);
    setShowLogin(false);
  };

  const handleWalletDisconnect = () => {
    consoleLog('HomePage - Wallet disconnected via AppShell');
    setShowLogin(true);
  };

  // Show login page if wallet is not connected
  if (showLogin || isDisconnected || !isConnected) {
    return <LoginPage onConnect={handleWalletConnect} />;
  }

  // Show loading state while connecting
  if (isConnecting) {
    return (
      <AppShellLayout onWalletDisconnect={handleWalletDisconnect}>
        <Center h={400}>
          <Stack align="center" gap="md">
            <Loader size="lg" />
            <Text size="lg">Connecting to wallet...</Text>
          </Stack>
        </Center>
      </AppShellLayout>
    );
  }

  // Main dashboard content
  return (
    <AppShellLayout onWalletDisconnect={handleWalletDisconnect}>
      <Container size="xl">
        <Stack gap="xl">
          {/* Welcome Header */}
          <Box>
            <Title order={1} mb="xs">
              Welcome to ODude Dealer
            </Title>
            <Text size="lg" c="dimmed">
              Your Top Level Name Management DApp
            </Text>
          </Box>

          {/* Wallet Info Cards */}
          <Grid>
            <Grid.Col span={{ base: 12, md: 4 }}>
              <Card shadow="sm" padding="lg" radius="md" withBorder>
                <Group justify="space-between" mb="xs">
                  <Text fw={500}>Wallet Address</Text>
                  <ThemeIcon color="blue" variant="light" radius="xl">
                    <IconWallet size={18} />
                  </ThemeIcon>
                </Group>
                <Text size="sm" c="dimmed" ff="monospace">
                  {address ? `${address.slice(0, 8)}...${address.slice(-8)}` : 'Not connected'}
                </Text>
                <Badge color="green" variant="light" size="sm" mt="sm">
                  Connected
                </Badge>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 4 }}>
              <Card shadow="sm" padding="lg" radius="md" withBorder>
                <Group justify="space-between" mb="xs">
                  <Text fw={500}>Network</Text>
                  <ThemeIcon color="violet" variant="light" radius="xl">
                    <IconNetwork size={18} />
                  </ThemeIcon>
                </Group>
                <Text size="sm" c="dimmed">
                  {chain ? getChainName(chain.id) : 'Unknown'}
                </Text>
                <Badge color="violet" variant="light" size="sm" mt="sm">
                  Chain ID: {chain?.id || 'Unknown'}
                </Badge>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 4 }}>
              <Card shadow="sm" padding="lg" radius="md" withBorder>
                <Group justify="space-between" mb="xs">
                  <Text fw={500}>Balance</Text>
                  <ThemeIcon color="green" variant="light" radius="xl">
                    <IconCoins size={18} />
                  </ThemeIcon>
                </Group>
                {balanceLoading ? (
                  <Loader size="sm" />
                ) : (
                  <>
                    <Text size="sm" c="dimmed">
                      {balance ? `${formatBalance(balance.formatted)} ${balance.symbol}` : '0.0000'}
                    </Text>
                    <Badge color="green" variant="light" size="sm" mt="sm">
                      Native Token
                    </Badge>
                  </>
                )}
              </Card>
            </Grid.Col>
          </Grid>

          {/* Activity Section */}
          <Paper shadow="sm" p="lg" radius="md" withBorder>
            <Group mb="md">
              <ThemeIcon color="orange" variant="light" radius="xl">
                <IconActivity size={20} />
              </ThemeIcon>
              <Title order={3}>Recent Activity</Title>
            </Group>
            <Text c="dimmed" ta="center" py="xl">
              No recent activity to display. Start using your wallet to see transactions here.
            </Text>
          </Paper>
        </Stack>
      </Container>
    </AppShellLayout>
  );
}
